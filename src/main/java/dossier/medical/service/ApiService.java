package dossier.medical.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import dossier.medical.model.PatientDTO;
import dossier.medical.model.VisiteRequeteDTO;
import dossier.medical.model.antecedent.AntecedentRequeteDTO;
import dossier.medical.model.authentification.AuthentificationReponseDTO;
import dossier.medical.model.authentification.AuthentificationRequeteDTO;
import dossier.medical.session.SessionManager;

import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;

public class ApiService {
    private static final String BASE_URL = "http://localhost:8080/api";
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public ApiService() {
        this.httpClient = HttpClient.newHttpClient();
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
    }
    
    public AuthentificationReponseDTO authenticate(String courriel, String motDePasse) throws Exception {
        var request = new AuthentificationRequeteDTO(courriel, motDePasse);
        String json = objectMapper.writeValueAsString(request);
        
        HttpRequest httpRequest = HttpRequest.newBuilder()
            .uri(URI.create(BASE_URL + "/authentification/connexion"))
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(json))
            .build();
            
        HttpResponse<String> response = httpClient.send(httpRequest, 
            HttpResponse.BodyHandlers.ofString());
            
        if (response.statusCode() == 200) {
            return objectMapper.readValue(response.body(), AuthentificationReponseDTO.class);
        }
        throw new RuntimeException("Authentification échouée");
    }
    
    public PatientDTO getPatientDetails(String identifiant) throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(BASE_URL + "/patients/" + identifiant))
            .header("Authorization", "Bearer " + SessionManager.getInstance().getJwtToken())
            .GET()
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        if (response.statusCode() == 200) {
            return objectMapper.readValue(response.body(), PatientDTO.class);
        }
        throw new RuntimeException("Patient non trouvé");
    }
    
    public AntecedentRequeteDTO ajouterAntecedent(AntecedentRequeteDTO antecedent) throws Exception {
        String json = objectMapper.writeValueAsString(antecedent);
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(BASE_URL + "/antecedents/ajouter"))
            .header("Content-Type", "application/json")
            .header("Authorization", "Bearer " + SessionManager.getInstance().getJwtToken())
            .POST(HttpRequest.BodyPublishers.ofString(json))
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        if (response.statusCode() == 200) {
            return objectMapper.readValue(response.body(), AntecedentRequeteDTO.class);
        }

        throw new RuntimeException("Erreur lors de l'ajout de l'antécédent");
    }
    
    public VisiteRequeteDTO ajouterVisite(VisiteRequeteDTO visite) throws Exception {
        String json = objectMapper.writeValueAsString(visite);
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(BASE_URL + "/visites/ajouter"))
            .header("Content-Type", "application/json")
            .header("Authorization", "Bearer " + SessionManager.getInstance().getJwtToken())
            .POST(HttpRequest.BodyPublishers.ofString(json))
            .build();
            
        HttpResponse<String> response = httpClient.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        if (response.statusCode() == 200) {
            return objectMapper.readValue(response.body(), VisiteRequeteDTO.class);
        }
        throw new RuntimeException("Erreur lors de l'ajout de la visite");
    }

   
}
