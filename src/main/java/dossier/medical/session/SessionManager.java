package dossier.medical.session;

public class SessionManager {
    private static SessionManager instance;
    private String jwtToken;
    private String nomComplet;
    private String role;
    private String currentPatientRamq;
    
    private SessionManager() {}
    
    public static SessionManager getInstance() {
        if (instance == null) {
            instance = new SessionManager();
        }
        return instance;
    }
    
    public void setAuthData(String token, String nomComplet, String role) {
        this.jwtToken = token;
        this.nomComplet = nomComplet;
        this.role = role;
    }
    
    public void setCurrentPatient(String ramq) {
        this.currentPatientRamq = ramq;
    }
    
    public String getJwtToken() { return jwtToken; }
    public String getNomComplet() { return nomComplet; }
    public String getRole() { return role; }
    public String getCurrentPatientRamq() { return currentPatientRamq; }
    
    public boolean isAuthenticated() {
        return jwtToken != null && !jwtToken.isEmpty();
    }
    
    public void logout() {
        jwtToken = null;
        nomComplet = null;
        role = null;
        currentPatientRamq = null;
    }
}