package dossier.medical.model;

import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import dossier.medical.model.antecedent.AntecedentDTO;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PatientDTO {
    private String idRamq;
    private String nom;
    private String prenom;
    private LocalDate dateNaissance;

    @JsonProperty("genre")
    private String sexe;

    private CoordonneesDTO coordonnees;

    @JsonProperty("dossierMedical")
    private DossierMedicalDTO dossierMedical;

    // Getters utiles directement
    public String getIdRamq() { return idRamq; }
    public void setIdRamq(String idRamq) { this.idRamq = idRamq; }

    public String getNom() { return nom; }
    public void setNom(String nom) { this.nom = nom; }

    public String getPrenom() { return prenom; }
    public void setPrenom(String prenom) { this.prenom = prenom; }

    public LocalDate getDateNaissance() { return dateNaissance; }
    public void setDateNaissance(LocalDate dateNaissance) { this.dateNaissance = dateNaissance; }

    public String getSexe() { return sexe; }
    public void setSexe(String sexe) { this.sexe = sexe; }

    public CoordonneesDTO getCoordonnees() { return coordonnees; }
    public void setCoordonnees(CoordonneesDTO coordonnees) { this.coordonnees = coordonnees; }

    public DossierMedicalDTO getDossierMedical() { return dossierMedical; }
    public void setDossierMedical(DossierMedicalDTO dossierMedical) { this.dossierMedical = dossierMedical; }

    // Raccourcis utiles
    public String getTelephone() {
        return coordonnees != null ? coordonnees.getTelephone() : null;
    }

    public String getCourriel() {
        return coordonnees != null ? coordonnees.getCourriel() : null;
    }

    public List<AntecedentDTO> getAntecedents() {
        return dossierMedical != null ? dossierMedical.getAntecedents() : null;
    }

    public List<VisiteDTO> getVisites() {
        return dossierMedical != null ? dossierMedical.getVisites() : null;
    }
}
