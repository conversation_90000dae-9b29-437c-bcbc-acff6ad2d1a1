package dossier.medical.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import dossier.medical.model.antecedent.AntecedentDTO;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DossierMedicalDTO {
    private Long id;
    private boolean actif;
    private List<AntecedentDTO> antecedents;
    private List<VisiteDTO> visites;

    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public boolean isActif() { return actif; }
    public void setActif(boolean actif) { this.actif = actif; }

    public List<AntecedentDTO> getAntecedents() { return antecedents; }
    public void setAntecedents(List<AntecedentDTO> antecedents) { this.antecedents = antecedents; }

    public List<VisiteDTO> getVisites() { return visites; }
    public void setVisites(List<VisiteDTO> visites) { this.visites = visites; }
}
