package dossier.medical.model.antecedent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AntecedentRequeteDTO {
    private Long id;
    private String diagnostic;
    private String traitement;
    private String dateDebut;  // format "yyyy-MM-dd"
    private String dateFin;    // format "yyyy-MM-dd" ou null
    private String identifiantPatient;

    public AntecedentRequeteDTO() {}

    public AntecedentRequeteDTO(String diagnostic, String traitement, String dateDebut, String dateFin, String identifiantPatient) {
        this.diagnostic = diagnostic;
        this.traitement = traitement;
        this.dateDebut = dateDebut;
        this.dateFin = dateFin;
        this.identifiantPatient = identifiantPatient;
    }

    // Getters/Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getDiagnostic() { return diagnostic; }
    public void setDiagnostic(String diagnostic) { this.diagnostic = diagnostic; }

    public String getTraitement() { return traitement; }
    public void setTraitement(String traitement) { this.traitement = traitement; }

    public String getDateDebut() { return dateDebut; }
    public void setDateDebut(String dateDebut) { this.dateDebut = dateDebut; }

    public String getDateFin() { return dateFin; }
    public void setDateFin(String dateFin) { this.dateFin = dateFin; }

    public String getIdentifiantPatient() { return identifiantPatient; }
    public void setIdentifiantPatient(String identifiantPatient) { this.identifiantPatient = identifiantPatient; }
}
