package dossier.medical.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ParentDTO {
    private Long id;
    private String prenom;
    private String nom;
    private String typeParente; // Ce champ est probablement une enum côté backend (TypeParente)
    private CoordonneesDTO coordonnees;

    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getPrenom() { return prenom; }
    public void setPrenom(String prenom) { this.prenom = prenom; }

    public String getNom() { return nom; }
    public void setNom(String nom) { this.nom = nom; }

    public String getTypeParente() { return typeParente; }
    public void setTypeParente(String typeParente) { this.typeParente = typeParente; }

    public CoordonneesDTO getCoordonnees() { return coordonnees; }
    public void setCoordonnees(CoordonneesDTO coordonnees) { this.coordonnees = coordonnees; }
}
