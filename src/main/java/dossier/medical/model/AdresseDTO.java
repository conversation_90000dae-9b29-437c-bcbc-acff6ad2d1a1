package dossier.medical.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AdresseDTO {
    private String numeroCivique;
    private String rue;
    private String numeroAppartement;
    private String ville;
    private String codePostal;
    private String province;
    private String pays;

    public String getNumeroCivique() { return numeroCivique; }
    public void setNumeroCivique(String numeroCivique) { this.numeroCivique = numeroCivique; }

    public String getRue() { return rue; }
    public void setRue(String rue) { this.rue = rue; }

    public String getNumeroAppartement() { return numeroAppartement; }
    public void setNumeroAppartement(String numeroAppartement) { this.numeroAppartement = numeroAppartement; }

    public String getVille() { return ville; }
    public void setVille(String ville) { this.ville = ville; }

    public String getCodePostal() { return codePostal; }
    public void setCodePostal(String codePostal) { this.codePostal = codePostal; }

    public String getProvince() { return province; }
    public void setProvince(String province) { this.province = province; }

    public String getPays() { return pays; }
    public void setPays(String pays) { this.pays = pays; }
}
