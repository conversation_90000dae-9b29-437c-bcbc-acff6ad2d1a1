package dossier.medical.model;

import java.time.LocalDate;
import java.time.LocalTime;

public class VisiteDTO {
    private Long id;
    private LocalDate dateVisite;
    private LocalTime heureVisite;
    private String motifVisite;
    private String diagnostic;
    private String traitement;
    private String notes;
    
    // Getters/Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public LocalDate getDateVisite() { return dateVisite; }
    public void setDateVisite(LocalDate dateVisite) { this.dateVisite = dateVisite; }
    public LocalTime getHeureVisite() { return heureVisite; }
    public void setHeureVisite(LocalTime heureVisite) { this.heureVisite = heureVisite; }
    public String getMotifVisite() { return motifVisite; }
    public void setMotifVisite(String motifVisite) { this.motifVisite = motifVisite; }
    public String getDiagnostic() { return diagnostic; }
    public void setDiagnostic(String diagnostic) { this.diagnostic = diagnostic; }
    public String getTraitement() { return traitement; }
    public void setTraitement(String traitement) { this.traitement = traitement; }
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
}