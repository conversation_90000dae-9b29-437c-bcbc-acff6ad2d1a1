package dossier.medical.controller;

import dossier.medical.App;
import dossier.medical.model.authentification.AuthentificationReponseDTO;
import dossier.medical.service.ApiService;
import dossier.medical.session.SessionManager;
import javafx.fxml.FXML;
import javafx.scene.control.TextField;
import javafx.scene.control.PasswordField;
import javafx.scene.control.Label;

public class LoginController {
    @FXML private TextField emailField;
    @FXML private PasswordField passwordField;
    @FXML private Label errorLabel;
    
    private ApiService apiService = new ApiService();
    
    @FXML
    private void handleLogin() {
        try {
            String email = emailField.getText();
            String password = passwordField.getText();
            
            AuthentificationReponseDTO response = apiService.authenticate(email, password);
            
            SessionManager.getInstance().setAuthData(
                response.getToken(), 
                response.getNomComplet(), 
                response.getRole()
            );
            
            App.setRoot("medecin-dashboard");
            
        } catch (Exception e) {
            errorLabel.setText("Erreur d'authentification");
        }
    }
}