package dossier.medical.controller;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.TextField;
import javafx.scene.control.Label;
import java.net.URL;
import java.util.ResourceBundle;

import dossier.medical.App;
import dossier.medical.model.PatientDTO;
import dossier.medical.service.ApiService;
import dossier.medical.session.SessionManager;



public class MedecinDashboardController implements Initializable {
    @FXML private Label welcomeLabel;
    @FXML private TextField ramqField;
    @FXML private Label errorLabel;
    
    private ApiService apiService = new ApiService();
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        SessionManager session = SessionManager.getInstance();
        welcomeLabel.setText("Bienvenue " + session.getNomComplet());
    }
    

    @FXML
    private void handlePatientSearch() {
        try {
            String ramq = ramqField.getText();

            PatientDTO patient = apiService.getPatientDetails(ramq);
            if (patient == null) {
                errorLabel.setText("Patient non trouvé");
                return;
            }

            SessionManager.getInstance().setCurrentPatient(ramq);

            App.setRoot("patient-dossier");

        } catch (Exception e) {
            //e.printStackTrace(); 
            errorLabel.setText("Une erreur est survenue lors de la recherche du patient " + e.getMessage());
        }
    }

    
    @FXML
    private void handleLogout() throws Exception {
        SessionManager.getInstance().logout();
        App.setRoot("login");
    }
}