package dossier.medical.controller;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import java.net.URL;
import java.util.ResourceBundle;

import dossier.medical.App;
import dossier.medical.model.PatientDTO;
import dossier.medical.model.VisiteDTO;
import dossier.medical.model.VisiteRequeteDTO;
import dossier.medical.model.antecedent.AntecedentDTO;
import dossier.medical.model.antecedent.AntecedentRequeteDTO;
import dossier.medical.service.ApiService;
import dossier.medical.session.SessionManager;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

public class PatientDossierController implements Initializable {
    @FXML private Label patientInfoLabel;
    @FXML private Label patientDetailsLabel;
    @FXML private TextField diagnosticField;
    @FXML private TextField traitementField;
    @FXML private DatePicker dateDebutPicker;
    @FXML private DatePicker dateFinPicker;
    @FXML private ListView<String> antecedentsList;
    
    // Nouveaux éléments pour les visites
    @FXML private DatePicker dateVisitePicker;
    @FXML private TextField heureVisiteField;
    @FXML private TextField motifVisiteField;
    @FXML private TextField diagnosticVisiteField;
    @FXML private TextField traitementVisiteField;
    @FXML private TextArea notesVisiteArea;
    @FXML private ListView<String> visitesList;
    
    @FXML private Label messageLabel;
    
    private ApiService apiService = new ApiService();
    private PatientDTO currentPatient;
    private ObservableList<String> antecedentsItems = FXCollections.observableArrayList();
    private ObservableList<String> visitesItems = FXCollections.observableArrayList();
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        antecedentsList.setItems(antecedentsItems);
        visitesList.setItems(visitesItems);
        loadPatientData();
    }
    
    private void loadPatientData() {
        try {
            String ramq = SessionManager.getInstance().getCurrentPatientRamq();
            currentPatient = apiService.getPatientDetails(ramq);
            
            patientInfoLabel.setText(String.format("%s %s - RAMQ: %s", 
                currentPatient.getPrenom(), 
                currentPatient.getNom(), 
                currentPatient.getIdRamq()));
            
            patientDetailsLabel.setText(String.format(
                "Nom: %s %s%nRAMQ: %s%nDate de naissance: %s%nSexe: %s%nTéléphone: %s%nCourriel: %s",
                currentPatient.getPrenom(),
                currentPatient.getNom(),
                currentPatient.getIdRamq(),
                currentPatient.getDateNaissance() != null ? currentPatient.getDateNaissance() : "Non renseigné",
                currentPatient.getSexe() != null ? currentPatient.getSexe() : "Non renseigné",
                currentPatient.getCoordonnees().getTelephone() != null ? currentPatient.getCoordonnees().getTelephone() : "Non renseigné",
                currentPatient.getCoordonnees().getCourriel() != null ? currentPatient.getCoordonnees().getCourriel() : "Non renseigné"
            ));

            
            updateAntecedentsList();
            updateVisitesList();
            
        } catch (Exception e) {
            messageLabel.setText("Erreur lors du chargement des données");
        }
    }
    
    private void updateAntecedentsList() {
        antecedentsItems.clear();
        if (currentPatient.getAntecedents() != null) {
            for (AntecedentDTO antecedent : currentPatient.getAntecedents()) {
                antecedentsItems.add(String.format("%s - %s (%s)", 
                    antecedent.getDiagnostic(), 
                    antecedent.getTraitement(),
                    antecedent.getDateDebut()));
            }
        }
    }
    
    private void updateVisitesList() {
        visitesItems.clear();
        if (currentPatient.getVisites() != null) {
            for (VisiteDTO visite : currentPatient.getVisites()) {
                visitesItems.add(String.format("%s %s - %s", 
                    visite.getDateVisite(),
                    visite.getHeureVisite() != null ? visite.getHeureVisite() : "",
                    visite.getMotifVisite()));
            }
        }
    }

@FXML
private void handleAjouterAntecedent() {
    try {
        String diagnostic = diagnosticField.getText();
        String traitement = traitementField.getText();
        LocalDate dateDebut = dateDebutPicker.getValue();
        LocalDate dateFin = dateFinPicker.getValue();

        if (diagnostic.isEmpty() || traitement.isEmpty() || dateDebut == null) {
            messageLabel.setText("Veuillez remplir tous les champs obligatoires");
            return;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dateDebutStr = dateDebut.format(formatter);
        String dateFinStr = dateFin != null ? dateFin.format(formatter) : null;

        AntecedentRequeteDTO antecedent = new AntecedentRequeteDTO(
            diagnostic, traitement, dateDebutStr, dateFinStr,
            SessionManager.getInstance().getCurrentPatientRamq()
        );

        apiService.ajouterAntecedent(antecedent);

        diagnosticField.clear();
        traitementField.clear();
        dateDebutPicker.setValue(null);
        dateFinPicker.setValue(null);

        loadPatientData();
        messageLabel.setText("Antécédent ajouté avec succès");

    } catch (Exception e) {
        e.printStackTrace();
        messageLabel.setText("Erreur lors de l'ajout de l'antécédent");
    }
}

    
    @FXML
    private void handleAjouterVisite() {
        try {
            LocalDate dateVisite = dateVisitePicker.getValue();
            String heureStr = heureVisiteField.getText();
            String motif = motifVisiteField.getText();
            String diagnostic = diagnosticVisiteField.getText();
            String traitement = traitementVisiteField.getText();
            String notes = notesVisiteArea.getText();
            
            if (dateVisite == null || motif.isEmpty()) {
                messageLabel.setText("Veuillez remplir les champs obligatoires (date et motif)");
                return;
            }
            
            LocalTime heureVisite = null;
            if (!heureStr.isEmpty()) {
                try {
                    heureVisite = LocalTime.parse(heureStr);
                } catch (Exception e) {
                    messageLabel.setText("Format d'heure invalide (HH:MM)");
                    return;
                }
            }
            
            VisiteRequeteDTO visite = new VisiteRequeteDTO(
                dateVisite, heureVisite, motif, diagnostic, traitement, notes,
                SessionManager.getInstance().getCurrentPatientRamq()
            );
            
            apiService.ajouterVisite(visite);
            
            // Réinitialiser les champs
            dateVisitePicker.setValue(null);
            heureVisiteField.clear();
            motifVisiteField.clear();
            diagnosticVisiteField.clear();
            traitementVisiteField.clear();
            notesVisiteArea.clear();
            
            // Recharger les données
            loadPatientData();
            messageLabel.setText("Visite ajoutée avec succès");
            
        } catch (Exception e) {
            messageLabel.setText("Erreur lors de l'ajout de la visite");
        }
    }
    
    @FXML
    private void handleRetourDashboard() throws Exception {
        App.setRoot("medecin-dashboard");
    }
    
    @FXML
    private void handleLogout() throws Exception {
        SessionManager.getInstance().logout();
        App.setRoot("login");
    }
}
