<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.geometry.Insets?>

<VBox spacing="15.0" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="dossier.medical.controller.PatientDossierController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Button text="← Retour" onAction="#handleRetourDashboard"/>
            <Region HBox.hgrow="ALWAYS"/>
            <Button text="Déconnexion" onAction="#handleLogout"/>
         </children>
      </HBox>
      
      <!-- Patient Info -->
      <Label fx:id="patientInfoLabel" style="-fx-font-size: 18px; -fx-font-weight: bold;"/>
      
      <!-- Tabs -->
      <TabPane>
         <tabs>
            <!-- Informations Patient -->
            <Tab text="Informations" closable="false">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <Label text="Informations du Patient" style="-fx-font-size: 16px;"/>
                        <Label fx:id="patientDetailsLabel" wrapText="true"/>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                     </padding>
                  </VBox>
               </content>
            </Tab>
            
            <!-- Ajouter Visite -->
            <Tab text="Ajouter Visite" closable="false">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <Label text="Nouvelle Visite" style="-fx-font-size: 16px;"/>
                        <HBox spacing="10.0">
                           <children>
                              <VBox>
                                 <children>
                                    <Label text="Date visite *"/>
                                    <DatePicker fx:id="dateVisitePicker"/>
                                 </children>
                              </VBox>
                              <VBox>
                                 <children>
                                    <Label text="Heure (HH:MM)"/>
                                    <TextField fx:id="heureVisiteField" promptText="14:30"/>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <TextField fx:id="motifVisiteField" promptText="Motif de la visite *"/>
                        <TextField fx:id="diagnosticVisiteField" promptText="Diagnostic"/>
                        <TextField fx:id="traitementVisiteField" promptText="Traitement"/>
                        <VBox>
                           <children>
                              <Label text="Notes"/>
                              <TextArea fx:id="notesVisiteArea" prefRowCount="3" promptText="Notes additionnelles..."/>
                           </children>
                        </VBox>
                        <Button text="Ajouter Visite" onAction="#handleAjouterVisite"/>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                     </padding>
                  </VBox>
               </content>
            </Tab>
            
            <!-- Ajouter Antécédent -->
            <Tab text="Ajouter Antécédent" closable="false">
               <content>
                  <VBox spacing="15.0">
                     <children>
                        <Label text="Nouveau Antécédent" style="-fx-font-size: 16px;"/>
                        <TextField fx:id="diagnosticField" promptText="Diagnostic *"/>
                        <TextField fx:id="traitementField" promptText="Traitement *"/>
                        <HBox spacing="10.0">
                           <children>
                              <VBox>
                                 <children>
                                    <Label text="Date début *"/>
                                    <DatePicker fx:id="dateDebutPicker"/>
                                 </children>
                              </VBox>
                              <VBox>
                                 <children>
                                    <Label text="Date fin"/>
                                    <DatePicker fx:id="dateFinPicker"/>
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Button text="Ajouter Antécédent" onAction="#handleAjouterAntecedent"/>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                     </padding>
                  </VBox>
               </content>
            </Tab>
            
            <!-- Liste Visites -->
            <Tab text="Visites" closable="false">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <Label text="Historique des Visites" style="-fx-font-size: 16px;"/>
                        <ListView fx:id="visitesList" prefHeight="300.0"/>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                     </padding>
                  </VBox>
               </content>
            </Tab>
            
            <!-- Liste Antécédents -->
            <Tab text="Antécédents" closable="false">
               <content>
                  <VBox spacing="10.0">
                     <children>
                        <Label text="Historique des Antécédents" style="-fx-font-size: 16px;"/>
                        <ListView fx:id="antecedentsList" prefHeight="300.0"/>
                     </children>
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0"/>
                     </padding>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>
      
      <Label fx:id="messageLabel" textFill="green"/>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0"/>
   </padding>
</VBox>
