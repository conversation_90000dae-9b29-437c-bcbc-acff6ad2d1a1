<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.control.*?>
<?import javafx.geometry.Insets?>

<VBox spacing="20.0" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="dossier.medical.controller.MedecinDashboardController">
   <children>
      <HBox alignment="CENTER_RIGHT">
         <children>
            <Button text="Déconnexion" onAction="#handleLogout"/>
         </children>
      </HBox>
      
      <VBox alignment="CENTER" spacing="20.0">
         <children>
            <Label fx:id="welcomeLabel" style="-fx-font-size: 24px;"/>
            <Label text="Recherche Patient" style="-fx-font-size: 18px;"/>
            <TextField fx:id="ramqField" promptText="Numéro RAMQ du patient"/>
            <Button text="Rechercher Patient" onAction="#handlePatientSearch"/>
            <Label fx:id="errorLabel" textFill="red"/>
         </children>
      </VBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0"/>
   </padding>
</VBox>