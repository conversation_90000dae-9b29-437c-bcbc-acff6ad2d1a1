<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.VBox?>
<?import javafx.scene.control.*?>
<?import javafx.geometry.Insets?>

<VBox alignment="CENTER" spacing="20.0" xmlns:fx="http://javafx.com/fxml/1" 
      fx:controller="dossier.medical.controller.LoginController">
   <children>
      <Label text="Connexion Médecin" style="-fx-font-size: 24px;"/>
      <TextField fx:id="emailField" promptText="Courriel"/>
      <PasswordField fx:id="passwordField" promptText="Mot de passe"/>
      <Button text="Se connecter" onAction="#handleLogin"/>
      <Label fx:id="errorLabel" textFill="red"/>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0"/>
   </padding>
</VBox>